import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 体重变化详情弹窗
class WeightChangeDialog extends StatelessWidget {
  final dynamic firstRecord;
  final dynamic lastRecord;
  final String firstDate;
  final String lastDate;
  final double weightChange;
  final bool isWeightLoss;
  final bool isWeightGain;

  const WeightChangeDialog({
    super.key,
    required this.firstRecord,
    required this.lastRecord,
    required this.firstDate,
    required this.lastDate,
    required this.weightChange,
    required this.isWeightLoss,
    required this.isWeightGain,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFFF8F9FA), Colors.white],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: isWeightLoss
                          ? [const Color(0xFF48BB78), const Color(0xFF38A169)]
                          : isWeightGain
                              ? [const Color(0xFFE53E3E), const Color(0xFFE53E3E)]
                              : [const Color(0xFF667EEA), const Color(0xFF764BA2)],
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    isWeightLoss
                        ? Icons.trending_down
                        : isWeightGain
                            ? Icons.trending_up
                            : Icons.trending_flat,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '体重变化详情',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                ),
                IconButton(
                  onPressed: () => Get.back(),
                  icon: Icon(Icons.close, color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // 体重变化信息
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.grey[200]!, width: 1),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // 最初体重
                  _buildWeightRow(
                    '最初体重',
                    '${(firstRecord.weight ?? 0.0).toStringAsFixed(1)}kg',
                    firstDate,
                    const Color(0xFF667EEA),
                  ),
                  const SizedBox(height: 16),

                  // 分隔线
                  Container(
                    height: 1,
                    color: Colors.grey[200],
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                  ),
                  const SizedBox(height: 16),

                  // 当前体重
                  _buildWeightRow(
                    '当前体重',
                    '${(lastRecord.weight ?? 0.0).toStringAsFixed(1)}kg',
                    lastDate,
                    const Color(0xFF764BA2),
                  ),
                  const SizedBox(height: 20),

                  // 结果
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: isWeightLoss
                          ? const Color(0xFF48BB78).withValues(alpha: 0.1)
                          : isWeightGain
                              ? const Color(0xFFE53E3E).withValues(alpha: 0.1)
                              : const Color(0xFF667EEA).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isWeightLoss
                            ? const Color(0xFF48BB78).withValues(alpha: 0.3)
                            : isWeightGain
                                ? const Color(0xFFE53E3E).withValues(alpha: 0.3)
                                : const Color(0xFF667EEA).withValues(alpha: 0.3),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.calculate,
                          color: isWeightLoss
                              ? const Color(0xFF48BB78)
                              : isWeightGain
                                  ? const Color(0xFFE53E3E)
                                  : const Color(0xFF667EEA),
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          weightChange == 0
                              ? '体重无变化'
                              : isWeightLoss
                                  ? '实现减重 ${(-weightChange).toStringAsFixed(1)}kg'
                                  : '增重 ${weightChange.toStringAsFixed(1)}kg',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: isWeightLoss
                                ? const Color(0xFF48BB78)
                                : isWeightGain
                                    ? const Color(0xFFE53E3E)
                                    : const Color(0xFF667EEA),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),

            // 关闭按钮
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF667EEA),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: const Text(
                  '知道了',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建体重行
  Widget _buildWeightRow(String label, String weight, String date, Color color) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                weight,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
        ),
        Text(
          date,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
