import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:get/get.dart';

import '../../../routes/app_pages.dart';
import '../controllers/my_controller.dart';

class MyView extends GetView<MyController> {
  const MyView({super.key});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      // appBar: AppBar(
      //   title: const Text('我的', style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: Color(0xFF2C3E50))),
      //   centerTitle: false,
      //   elevation: 0,
      //   backgroundColor: Colors.white,
      //   systemOverlayStyle: SystemUiOverlayStyle.dark,
      //   flexibleSpace: Container(
      //     decoration: const BoxDecoration(
      //       gradient: LinearGradient(
      //         begin: Alignment.topLeft,
      //         end: Alignment.bottomRight,
      //         colors: [Colors.white, Color(0xFFF8F9FA)],
      //       ),
      //     ),
      //   ),
      // ),
      body: Safe<PERSON><PERSON>(
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    // 个人信息卡片
                    Container(
                      margin: const EdgeInsets.fromLTRB(16, 8, 16, 12),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [Colors.white, Color(0xFFFAFBFC)],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.08),
                            blurRadius: 12,
                            offset: const Offset(0, 4),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                // 头像部分
                                // GestureDetector(
                                //   onTap: () => Get.toNamed(Routes.PERSONAL_INFO),
                                //   child: Container(
                                //     width: 64,
                                //     height: 64,
                                //     decoration: BoxDecoration(
                                //       shape: BoxShape.circle,
                                //       gradient: const LinearGradient(
                                //         begin: Alignment.topLeft,
                                //         end: Alignment.bottomRight,
                                //         colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                                //       ),
                                //       boxShadow: [
                                //         BoxShadow(
                                //           color: const Color(0xFF667EEA).withValues(alpha: 0.3),
                                //           blurRadius: 8,
                                //           offset: const Offset(0, 4),
                                //         ),
                                //       ],
                                //     ),
                                //     child: const Icon(Icons.person, size: 32, color: Colors.white),
                                //   ),
                                // ),
                                // const SizedBox(width: 16),
                                // 用户详情
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      GestureDetector(
                                        onTap: () => Get.toNamed(Routes.PERSONAL_INFO),
                                        child: Obx(
                                          () => Text(
                                            controller.userData['nickname'] ?? '完善信息',
                                            style: const TextStyle(
                                              fontSize: 20,
                                              fontWeight: FontWeight.w700,
                                              color: Color(0xFF2C3E50),
                                            ),
                                          ),
                                        ),
                                      ),
                                      // const SizedBox(height: 6),
                                      // Text(
                                      //   'ID: 389425',
                                      //   style: TextStyle(
                                      //     fontSize: 13,
                                      //     color: Colors.grey[600],
                                      //     fontWeight: FontWeight.w500,
                                      //   ),
                                      // ),
                                      // const SizedBox(height: 8),
                                      // 标签行
                                      // Wrap(
                                      //   spacing: 8,
                                      //   runSpacing: 6,
                                      //   children: [
                                      //     _buildTag('VIP会员', const Color(0xFF667EEA)),
                                      //     _buildTag('活跃用户', const Color(0xFF48BB78)),
                                      //   ],
                                      // ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            // 统计信息
                            Container(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF8F9FA),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  Obx(() => _buildStatItem('${controller.recordDays}', '记录天数')),
                                  _buildDivider(),
                                  Obx(() => _buildStatItem('${controller.continuousCheckIn}', '连续打卡')),
                                  _buildDivider(),
                                  Obx(() => _buildWeightChangeItem()),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 身体数据卡片
                    Container(
                      margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.06),
                            blurRadius: 10,
                            offset: const Offset(0, 3),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  '身体数据',
                                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Color(0xFF2C3E50)),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    // gradient: const LinearGradient(
                                    //   colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                                    // ), // 渐变背景
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(20),
                                      onTap: () => Get.toNamed(Routes.BODY_DATA_FORM),
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: const [
                                            Text(
                                              "更新",
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 13,
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                            SizedBox(width: 4),
                                            Icon(Icons.arrow_forward_ios, size: 12, color: Colors.black),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            GridView.count(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              crossAxisCount: 2,
                              childAspectRatio: 2.2,
                              crossAxisSpacing: 12,
                              mainAxisSpacing: 12,
                              children: [
                                Obx(() => _buildMetricCard(Icons.scale, '体重', controller.bodyData['width'], 'kg')),
                                Obx(
                                  () => _buildMetricCard(Icons.straighten, '身高', controller.bodyData['height'], 'cm'),
                                ),
                                Obx(() => _buildMetricCard(Icons.favorite, 'BMI', controller.bodyData['bmi'], '')),
                                Obx(
                                  () => _buildMetricCard(
                                    Icons.accessibility,
                                    '体脂率',
                                    controller.bodyData['bodyFatPercentage'],
                                    '%',
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 设置菜单
                    Container(
                      margin: const EdgeInsets.fromLTRB(16, 0, 16, 12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.06),
                            blurRadius: 10,
                            offset: const Offset(0, 3),
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              '设置',
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: Color(0xFF2C3E50)),
                            ),
                            const SizedBox(height: 16),
                            Column(
                              children: [
                                _buildMenuItem(
                                  Icons.flag_outlined,
                                  '目标设定',
                                  const Color(0xFF667EEA),
                                  onTap: () => Get.toNamed(Routes.GOAL_SETTING),
                                ),
                                // _buildMenuItem(
                                //   Icons.notifications_none,
                                //   '提醒设置',
                                //   const Color(0xFFED8936),
                                //   onTap: () => Get.toNamed(Routes.REMINDER_SETTING),
                                // ),
                                // _buildMenuItem(
                                //   Icons.palette_outlined,
                                //   '主题设置',
                                //   const Color(0xFF48BB78),
                                //   onTap: () => Get.toNamed(Routes.THEME_SETTING),
                                // ),
                                // _buildMenuItem(
                                //   Icons.security,
                                //   '隐私设置',
                                //   const Color(0xFFE53E3E),
                                //   onTap: () => Get.toNamed(Routes.PRIVACY_SETTING),
                                // ),
                                _buildMenuItem(
                                  Icons.help_outline,
                                  '帮助与反馈',
                                  const Color(0xFF805AD5),
                                  onTap: () => Get.toNamed(Routes.HELP_FEEDBACK),
                                ),
                                _buildMenuItem(
                                  Icons.info_outline,
                                  '关于我们',
                                  const Color(0xFF38B2AC),
                                  onTap: () => Get.toNamed(Routes.ABOUT_US),
                                  isLast: true,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // 版本信息
                    Container(
                      margin: const EdgeInsets.only(bottom: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Obx(
                            () => Text(
                              '当前版本: ${controller.fullVersion}',
                              style: TextStyle(fontSize: 13, color: Colors.grey[500], fontWeight: FontWeight.w500),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String value, String label, {bool isHighlight = false}) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w700,
            color: isHighlight ? const Color(0xFF667EEA) : const Color(0xFF2C3E50),
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 13, color: Colors.grey[600], fontWeight: FontWeight.w500)),
      ],
    );
  }

  /// 构建体重变化项目，根据增重或减重显示不同颜色和文本
  Widget _buildWeightChangeItem() {
    final weightChange = controller.weightChange;

    String displayText;
    String label;
    Color textColor;

    if (weightChange == 0) {
      displayText = '0kg';
      label = '体重变化';
      textColor = const Color(0xFF2C3E50);
    } else if (weightChange > 0) {
      // 增重，显示红色
      displayText = '+${weightChange.toStringAsFixed(1)}kg';
      label = '增重';
      textColor = const Color(0xFFE53E3E);
    } else {
      // 减重，显示绿色
      displayText = '-${(-weightChange).toStringAsFixed(1)}kg';
      label = '已减重';
      textColor = const Color(0xFF48BB78);
    }

    return GestureDetector(
      onTap: () => controller.showWeightChangeDetail(),
      child: Column(
        children: [
          Text(displayText, style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700, color: textColor)),
          const SizedBox(height: 4),
          Text(label, style: TextStyle(fontSize: 13, color: Colors.grey[600], fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  Widget _buildDivider() {
    return Container(width: 1, height: 24, color: Colors.grey[300]);
  }

  /// 构建指标卡片组件
  ///
  /// [icon] 图标
  /// [label] 标签文本
  /// [value] 数值（可能为null）
  /// [unit] 单位
  Widget _buildMetricCard(IconData icon, String label, String? value, String unit) {
    // 安全处理value值，确保null安全
    final String safeValue = value?.trim() ?? '-';
    final bool hasValidValue = safeValue.isNotEmpty && safeValue != '-' && safeValue != '-' && safeValue != 'null';

    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFF8F9FA), Colors.white],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 1),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          children: [
            // Container(
            //   width: 40,
            //   height: 40,
            //   decoration: BoxDecoration(
            //     gradient: const LinearGradient(colors: [Color(0xFF667EEA), Color(0xFF764BA2)]),
            //     shape: BoxShape.circle,
            //     boxShadow: [
            //       BoxShadow(
            //         color: const Color(0xFF667EEA).withValues(alpha: 0.3),
            //         blurRadius: 6,
            //         offset: const Offset(0, 2),
            //       ),
            //     ],
            //   ),
            //   child: Icon(icon, size: 20, color: Colors.white),
            // ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600], fontWeight: FontWeight.w500)),
                  const SizedBox(height: 2),
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: hasValidValue ? safeValue : '-',
                          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w700, color: Color(0xFF2C3E50)),
                        ),
                        if (unit.isNotEmpty && hasValidValue)
                          TextSpan(
                            text: unit,
                            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500, color: Colors.grey[600]),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuItem(IconData icon, String text, Color iconColor, {VoidCallback? onTap, bool isLast = false}) {
    return Container(
      margin: EdgeInsets.only(bottom: isLast ? 0 : 8),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!, width: 0.5),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(color: iconColor.withValues(alpha: 0.1), shape: BoxShape.circle),
                  child: Icon(icon, size: 20, color: iconColor),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    text,
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Color(0xFF2C3E50)),
                  ),
                ),
                Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 标签构建方法
  Widget _buildTag(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(colors: [color.withValues(alpha: 0.1), color.withValues(alpha: 0.05)]),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2), width: 1),
      ),
      child: Text(text, style: TextStyle(fontSize: 11, color: color, fontWeight: FontWeight.w600)),
    );
  }
}
