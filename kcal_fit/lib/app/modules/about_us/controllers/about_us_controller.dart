import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../../core/utils/logger_util.dart';
import '../../../shared/controllers/base_controller.dart';

/// 关于我们控制器
/// 负责管理关于我们页面的应用信息展示和功能特性介绍
class AboutUsController extends BaseController {
  // 应用信息
  final RxString _appName = 'KcalFit'.obs;
  final RxString _appVersion = '1.0.0'.obs;
  final RxString _buildNumber = '1'.obs;
  final RxString _packageName = ''.obs;

  // Getter方法，提供对私有变量的只读访问
  String get appName => _appName.value;
  String get appVersion => _appVersion.value;
  String get buildNumber => _buildNumber.value;
  String get packageName => _packageName.value;

  // 功能特性列表
  final List<Map<String, dynamic>> features = [
    {'icon': Icons.restaurant_menu, 'title': '智能饮食记录', 'description': '快速记录每日饮食，自动计算卡路里摄入'},
    {'icon': Icons.analytics, 'title': '数据分析', 'description': '详细的营养分析和趋势图表'},
    {'icon': Icons.fitness_center, 'title': '健康目标', 'description': '个性化的减重和健康目标设定'},
    // {'icon': Icons.notifications, 'title': '智能提醒', 'description': '贴心的饮食和运动提醒功能'},
  ];

  // 团队信息
  final List<Map<String, String>> teamInfo = [
    {'role': '产品设计', 'description': '专注用户体验，打造简洁易用的界面'},
    {'role': '技术开发', 'description': '采用最新技术栈，确保应用稳定流畅'},
    {'role': '营养专家', 'description': '提供专业的营养知识和健康建议'},
  ];

  @override
  void onInit() {
    super.onInit();
    _loadAppInfo();
  }

  @override
  Future<void> onRefresh() async {
    await _loadAppInfo();
  }

  /// 加载应用信息
  /// 从设备获取应用的基本信息，如版本号、包名等
  Future<void> _loadAppInfo() async {
    await executeAsync(() async {
      // 获取应用包信息
      final packageInfo = await PackageInfo.fromPlatform();

      _appName.value = packageInfo.appName;
      _appVersion.value = packageInfo.version;
      _buildNumber.value = packageInfo.buildNumber;
      _packageName.value = packageInfo.packageName;
      LoggerUtil.i('应用信息加载成功: ${packageInfo.appName} v${packageInfo.version}');
    });
  }

  /// 检查更新
  /// 检查应用是否有新版本可用
  void checkForUpdates() {
    // TODO: 实现真实的版本检查逻辑
    showInfo('当前已是最新版本');
  }

  /// 查看隐私政策
  /// 显示应用的隐私政策内容
  void viewPrivacyPolicy() {
    _showInfoDialog(
      title: '隐私政策',
      content:
          '我们非常重视您的隐私保护。本应用收集的个人信息仅用于提供更好的服务体验。\n\n'
          '收集的信息包括：\n'
          '• 基本个人信息（昵称、性别、年龄等）\n'
          '• 身体数据（身高、体重、BMI等）\n'
          '• 饮食记录数据\n\n'
          '我们承诺：\n'
          '• 不会将您的个人信息提供给第三方\n'
          '• 采用加密技术保护数据安全\n'
          '• 您可以随时删除个人数据\n\n'
          '如有疑问，请联系我们的客服团队。',
    );
  }

  /// 查看用户协议
  /// 显示应用的用户协议和使用条款
  void viewUserAgreement() {
    _showInfoDialog(
      title: '用户协议',
      content:
          '欢迎使用KcalFit应用！\n\n'
          '使用条款：\n'
          '1. 本应用提供的健康建议仅供参考，不能替代专业医疗建议\n'
          '2. 用户应确保输入数据的准确性\n'
          '3. 禁止利用本应用进行任何违法活动\n'
          '4. 我们保留随时修改服务条款的权利\n\n'
          '免责声明：\n'
          '• 本应用不对因使用应用而产生的任何直接或间接损失承担责任\n'
          '• 应用中的营养数据来源于公开数据库，可能存在误差\n'
          '• 建议在专业人士指导下制定健康计划\n\n'
          '如有疑问，请联系客服。',
    );
  }

  /// 联系我们
  /// 显示联系方式的底部弹窗
  void contactUs() {
    _showContactBottomSheet();
  }

  /// 构建联系方式项目
  Widget _buildContactItem(IconData icon, String title, String content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue.shade600, size: 20),
          const SizedBox(width: 12),
          Text(title, style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 14)),
          const SizedBox(width: 16),
          Expanded(child: Text(content, style: TextStyle(color: Colors.grey[600], fontSize: 14))),
        ],
      ),
    );
  }

  /// 显示联系方式底部弹窗
  /// 展示应用的联系方式信息
  void _showContactBottomSheet() {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(24),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(2)),
            ),
            const SizedBox(height: 20),
            const Text('联系我们', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600)),
            const SizedBox(height: 20),
            _buildContactItem(Icons.email, '邮箱', '<EMAIL>'),
            _buildContactItem(Icons.wechat, '客服微信', 'BD6JDO'),
            _buildContactItem(Icons.access_time, '工作时间', '周一至周六 9:00-18:00'),
            const SizedBox(height: 20),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => Get.back(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
                child: const Text('知道了'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示信息对话框
  /// 通用的信息展示对话框，用于显示隐私政策、用户协议等内容
  void _showInfoDialog({required String title, required String content}) {
    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: SingleChildScrollView(child: Text(content, style: const TextStyle(height: 1.5))),
        actions: [TextButton(onPressed: () => Get.back(), child: const Text('我知道了'))],
      ),
    );
  }
}
