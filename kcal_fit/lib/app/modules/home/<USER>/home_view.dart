import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:animated_bottom_navigation_bar/animated_bottom_navigation_bar.dart';

import '../../index/views/index_view.dart';
import '../../my/views/my_view.dart';
import '../controllers/home_controller.dart';
import '../../../routes/app_pages.dart';

/// 添加菜单项数据模型
class _AddMenuItem {
  const _AddMenuItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    this.route,
  });

  final IconData icon;
  final String title;
  final String subtitle;
  final Color color;
  final String? route;
}

/// 主页视图 - 包含底部导航栏和浮动操作按钮
class HomeView extends GetView<HomeController> {
  const HomeView({super.key});

  // ==================== 常量定义 ====================

  /// 主题色
  static const Color _primaryColor = Color(0xFF2196F3);
  static const Color _primaryDarkColor = Color(0xFF1976D2);

  /// 导航栏配置
  static const double _navigationBarHeight = 70.0;
  static const double _navigationBarRadius = 16.0;
  static const double _iconSize = 24.0;
  static const int _animationDuration = 300;

  /// 浮动按钮配置
  static const double _fabSize = 56.0;
  static const double _fabRadius = 28.0;
  static const double _fabIconSize = 28.0;

  /// 底部弹出框配置
  static const double _bottomSheetRadius = 20.0;
  static const double _menuItemRadius = 12.0;
  static const double _menuIconSize = 48.0;

  /// 阴影配置
  static const List<BoxShadow> _navigationShadows = [
    BoxShadow(color: Color.fromRGBO(0, 0, 0, 0.08), blurRadius: 24, offset: Offset(0, -8), spreadRadius: 0),
    BoxShadow(color: Color.fromRGBO(0, 0, 0, 0.04), blurRadius: 12, offset: Offset(0, -4), spreadRadius: 0),
  ];

  static const List<BoxShadow> _fabShadows = [
    BoxShadow(color: Color.fromRGBO(33, 150, 243, 0.3), blurRadius: 8, offset: Offset(0, 4)),
  ];

  /// 导航图标配置
  static const List<IconData> _navigationIcons = [Icons.home_outlined, Icons.person_outline];

  static const List<IconData> _activeNavigationIcons = [Icons.home, Icons.person];

  /// 导航标签配置
  static const List<String> _navigationLabels = ['首页', '我的'];

  /// 添加菜单配置
  static const List<_AddMenuItem> _addMenuItems = [
    _AddMenuItem(
      icon: Icons.restaurant,
      title: '饮食记录',
      subtitle: '记录今日饮食摄入',
      color: Color(0xFF4CAF50),
      route: Routes.ADD_DIET_RECORD,
    ),
    _AddMenuItem(
      icon: Icons.monitor_weight,
      title: '体重记录',
      subtitle: '记录当前体重数据',
      color: Color(0xFF2196F3),
      route: Routes.BODY_DATA_FORM,
    ),
    _AddMenuItem(
      icon: Icons.smart_toy,
      title: '智能助手',
      subtitle: '与AI助手聊天咨询',
      color: Color(0xFF9C27B0),
      route: Routes.AI_ASSISTANT,
    ),
    // _AddMenuItem(
    //   icon: Icons.fitness_center,
    //   title: '运动记录',
    //   subtitle: '记录运动消耗',
    //   color: Color(0xFFFF9800),
    //   route: null, // 开发中
    // ),
  ];

  // ==================== 主构建方法 ====================

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(child: _buildBody()),
      bottomNavigationBar: Obx(() => _buildAnimatedBottomBar()),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  // ==================== 私有构建方法 ====================

  /// 构建主体内容
  Widget _buildBody() {
    return Obx(() {
      switch (controller.currentIndex) {
        case 0:
          return IndexView();
        case 1:
          return const MyView();
        default:
          return const Center(child: Text('页面不存在', style: TextStyle(fontSize: 16, color: Colors.grey)));
      }
    });
  }

  /// 构建动画底部导航栏
  Widget _buildAnimatedBottomBar() {
    return Container(
      decoration: const BoxDecoration(boxShadow: _navigationShadows),
      child: AnimatedBottomNavigationBar.builder(
        itemCount: _navigationIcons.length,
        tabBuilder: _buildNavigationTab,
        activeIndex: controller.currentIndex,
        onTap: controller.changePage,
        backgroundColor: Colors.white,
        elevation: 0,
        height: 45,
        gapLocation: GapLocation.center,
        notchSmoothness: NotchSmoothness.defaultEdge,
        leftCornerRadius: 10,
        rightCornerRadius: 10,
        splashColor: _primaryColor.withValues(alpha: 0.1),
        splashSpeedInMilliseconds: _animationDuration,
      ),
    );
  }

  /// 构建导航标签
  Widget _buildNavigationTab(int index, bool isActive) {
    final icon = isActive ? _activeNavigationIcons[index] : _navigationIcons[index];
    final color = isActive ? _primaryColor : Colors.grey[400]!;
    final label = _navigationLabels[index];

    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(icon, size: _iconSize, color: color),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(fontSize: 12, color: color, fontWeight: isActive ? FontWeight.w600 : FontWeight.normal),
        ),
      ],
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return Container(
      width: _fabSize,
      height: _fabSize,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [_primaryColor, _primaryDarkColor],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(_fabRadius),
        boxShadow: _fabShadows,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(_fabRadius),
          onTap: _showAddBottomSheet,
          child: const Icon(Icons.add, color: Colors.white, size: _fabIconSize),
        ),
      ),
    );
  }

  // ==================== 事件处理方法 ====================

  /// 显示添加功能的底部弹出框
  void _showAddBottomSheet() {
    Get.bottomSheet(_buildAddBottomSheet(), backgroundColor: Colors.transparent, isScrollControlled: true);
  }

  /// 构建添加功能底部弹出框
  Widget _buildAddBottomSheet() {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(_bottomSheetRadius),
          topRight: Radius.circular(_bottomSheetRadius),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [_buildDragIndicator(), _buildSheetTitle(), _buildMenuItems(), const SizedBox(height: 30)],
        ),
      ),
    );
  }

  /// 构建拖拽指示器
  Widget _buildDragIndicator() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      width: 40,
      height: 4,
      decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(2)),
    );
  }

  /// 构建弹出框标题
  Widget _buildSheetTitle() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Text('添加记录', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600, color: Colors.grey[800])),
    );
  }

  /// 构建菜单项列表
  Widget _buildMenuItems() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children:
            _addMenuItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return Column(children: [if (index > 0) const SizedBox(height: 12), _buildAddMenuItem(item)]);
            }).toList(),
      ),
    );
  }

  /// 构建添加菜单项
  Widget _buildAddMenuItem(_AddMenuItem item) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _handleMenuItemTap(item),
        borderRadius: BorderRadius.circular(_menuItemRadius),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(_menuItemRadius),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              Container(
                width: _menuIconSize,
                height: _menuIconSize,
                decoration: BoxDecoration(
                  color: item.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(_menuItemRadius),
                ),
                child: Icon(item.icon, color: item.color, size: 24),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.title,
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Colors.grey[800]),
                    ),
                    const SizedBox(height: 4),
                    Text(item.subtitle, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
                  ],
                ),
              ),
              Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理菜单项点击事件
  void _handleMenuItemTap(_AddMenuItem item) {
    Get.back(); // 关闭底部弹出框

    if (item.route != null) {
      Get.toNamed(item.route!);
    } else {
      // 开发中的功能
      Get.snackbar('提示', '${item.title}功能开发中...');
    }
  }
}
