import 'package:get/get.dart';

import '../../../../core/utils/logger_util.dart';
import '../../../../core/services/user_guide_service.dart';
import '../../../shared/controllers/base_controller.dart';
import '../../index/controllers/index_controller.dart';
import '../../my/controllers/my_controller.dart';

/// 主页控制器
/// 负责管理底部导航栏的页面切换和数据刷新
class HomeController extends BaseController {
  /// 当前选中的页面索引
  final RxInt _currentIndex = 0.obs;

  /// 页面标题列表
  /// 由于使用浮动操作按钮，只有两个主要页面
  final List<String> pageTitles = ['首页', '我的'];

  // Getter方法，提供对私有变量的只读访问
  int get currentIndex => _currentIndex.value;

  @override
  void onInit() {
    super.onInit();
    LoggerUtil.d('主页控制器初始化，当前页面索引: ${_currentIndex.value}');

    // 检查并显示用户引导
    _checkUserGuide();
  }

  /// 检查用户引导
  void _checkUserGuide() {
    try {
      if (Get.isRegistered<UserGuideService>()) {
        final userGuideService = Get.find<UserGuideService>();
        userGuideService.checkAndShowGuide();
      }
    } catch (e) {
      LoggerUtil.e('检查用户引导失败: $e');
    }
  }

  /// 切换页面
  /// [index] 目标页面索引
  void changePage(int index) {
    if (index < 0 || index >= pageTitles.length) {
      LoggerUtil.w('无效的页面索引: $index');
      return;
    }

    if (_currentIndex.value == index) {
      // 如果是当前页面，不执行刷新操作，避免不必要的性能消耗
      LoggerUtil.d('已在当前页面: ${pageTitles[index]}');
      return;
    }

    LoggerUtil.d('切换页面: ${pageTitles[_currentIndex.value]} -> ${pageTitles[index]}');

    // 执行页面切换前的操作
    _onPageWillChange(index);

    // 更新当前页面索引
    _currentIndex.value = index;

    // 延迟执行页面切换后的操作，避免阻塞UI
    Future.microtask(() => _onPageDidChange(index));
  }

  /// 页面切换前的操作
  /// [index] 目标页面索引
  void _onPageWillChange(int index) {
    // 可以在这里添加页面切换前的通用逻辑
    LoggerUtil.d('准备切换到页面: ${pageTitles[index]}');
  }

  /// 页面切换后的操作
  /// [index] 当前页面索引
  void _onPageDidChange(int index) {
    switch (index) {
      case 0:
        // 首页 - 刷新今日数据
        _refreshIndexPage();
        break;
      case 1:
        // 我的页面 - 加载用户数据
        _refreshMyPage();
        break;
      default:
        LoggerUtil.w('未处理的页面索引: $index');
        break;
    }
  }

  /// 刷新当前页面
  /// [index] 当前页面索引
  void _refreshCurrentPage(int index) {
    LoggerUtil.d('刷新当前页面: ${pageTitles[index]}');
    _onPageDidChange(index);
  }

  /// 刷新首页数据
  void _refreshIndexPage() {
    try {
      if (Get.isRegistered<IndexController>()) {
        // 延迟刷新，避免阻塞页面切换
        Future.delayed(const Duration(milliseconds: 50), () {
          Get.find<IndexController>().onTabSelected();
        });
        LoggerUtil.d('首页数据刷新已调度');
      } else {
        LoggerUtil.w('IndexController未注册');
      }
    } catch (e) {
      LoggerUtil.e('刷新首页数据失败: $e');
    }
  }

  /// 刷新我的页面数据
  void _refreshMyPage() {
    try {
      if (Get.isRegistered<MyController>()) {
        Get.find<MyController>().refreshData();
        LoggerUtil.d('我的页面数据刷新成功');
      } else {
        LoggerUtil.w('MyController未注册');
      }
    } catch (e) {
      LoggerUtil.e('刷新我的页面数据失败: $e');
    }
  }

  /// 手动刷新当前页面数据
  /// 提供给外部调用的刷新方法
  void refreshCurrentPageData() {
    _refreshCurrentPage(_currentIndex.value);
  }

  @override
  Future<void> onRefresh() async {
    refreshCurrentPageData();
  }

  /// 获取当前页面标题
  String getCurrentPageTitle() {
    return pageTitles[_currentIndex.value];
  }

  /// 是否为指定页面
  /// [index] 页面索引
  bool isCurrentPage(int index) {
    return _currentIndex.value == index;
  }

  /// 返回首页
  void goToHomePage() {
    changePage(0);
  }

  /// 显示添加功能的底部弹出框
  void showAddBottomSheet() {
    LoggerUtil.d('显示添加功能底部弹出框');
    // 这个方法将在View中实现具体的UI
  }
}
