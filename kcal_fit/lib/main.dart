import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:health/health.dart';

import 'app/modules/index/controllers/index_controller.dart';
import 'app/routes/app_pages.dart';
import 'core/constants/locales.g.dart';
import 'core/init/app_initializer.dart';
import 'core/services/theme_service.dart';

final health = Health();

/// 应用程序主入口
/// 负责初始化应用并启动GetMaterialApp
void main() async {
  await AppInitializer.initialize();
  runApp(const KcalFitApp());
  _initializeHealthData();
}

/// 初始化健康数据获取
/// 在应用启动后异步获取健康数据，包含完善的权限处理和错误处理
Future<void> _initializeHealthData() async {
  try {
    final types = [
      HealthDataType.EXERCISE_TIME, // 运动时间
      HealthDataType.ACTIVE_ENERGY_BURNED, // 活动消耗的能量
      HealthDataType.BASAL_ENERGY_BURNED, // 基础代谢率
      HealthDataType.STEPS, // 步数
      HealthDataType.DISTANCE_WALKING_RUNNING, // 步行和跑步的距离
      HealthDataType.FLIGHTS_CLIMBED, // 上楼的楼层
    ];

    // 请求权限
    bool hasAccess = await Health().requestAuthorization(types);

    // 在获取健康数据后添加计算逻辑
    if (hasAccess) {
      print('健康数据权限获取成功');

      final now = DateTime.now();
      final healthData = await Health().getHealthDataFromTypes(
        types: types,
        startTime: now.subtract(const Duration(days: 1)),
        endTime: now,
      );
      // print('成功获取健康数据: ${healthData.toString()} 条记录');

      // 计算总卡路里
      double totalCalories = 0;
      double activeCalories = 0;
      double basalCalories = 0;

      for (var data in healthData) {
        print('健康数据类型: ${data.type}');
        print('健康数据值: ${data.value}');
        print('健康数据单位: ${data.unit}');
        print('健康数据来源: ${data.sourceName}');
        print('健康数据开始时间: ${data.dateFrom}');
        print('健康数据结束时间: ${data.dateTo}');

        try {
          double value = 0;

          // 安全地获取数值
          if (data.value is NumericHealthValue) {
            value = (data.value as NumericHealthValue).numericValue.toDouble();
          } else {
            print('未知的健康数据值类型: ${data.value.runtimeType}');
            continue;
          }

          if (data.type == HealthDataType.ACTIVE_ENERGY_BURNED) {
            activeCalories += value;
            // 显示详细时间信息（开始时间和结束时间）
            final startTime = data.dateFrom.toString().substring(0, 19);
            final endTime = data.dateTo.toString().substring(0, 19);
            print('活动消耗卡路里: $value kcal');
            print('  记录时间: $startTime 至 $endTime');
            print('  数据来源: ${data.sourceName}');
          } else if (data.type == HealthDataType.BASAL_ENERGY_BURNED) {
            basalCalories += value;
            // 显示详细时间信息（开始时间和结束时间）
            final startTime = data.dateFrom.toString().substring(0, 19);
            final endTime = data.dateTo.toString().substring(0, 19);
            print('基础代谢卡路里: $value kcal');
            print('  记录时间: $startTime 至 $endTime');
            print('  数据来源: ${data.sourceName}');
          }
        } catch (e) {
          print('处理健康数据时出错: ${data.type} - $e');
        }
      }
      totalCalories = activeCalories + basalCalories;
      print('总卡路里消耗: $totalCalories kcal');
    } else {
      print('健康数据权限被拒绝，请在设置中手动开启权限');
      _showPermissionDeniedDialog();
    }
  } catch (e) {
    print('获取健康数据时发生错误: $e');
    _handleHealthDataError(e);
  }
}

/// 处理健康数据
/// 对获取到的健康数据进行分类和处理
void _processHealthData(List<HealthDataPoint> healthData) {
  // 按数据类型分组处理
  final Map<HealthDataType, List<HealthDataPoint>> groupedData = {};

  for (var dataPoint in healthData) {
    if (!groupedData.containsKey(dataPoint.type)) {
      groupedData[dataPoint.type] = [];
    }
    groupedData[dataPoint.type]!.add(dataPoint);
  }

  // 输出各类型数据统计
  groupedData.forEach((type, data) {
    print('${type.name}: ${data.length} 条记录');
  });
}

/// 显示权限被拒绝的对话框
/// 引导用户手动开启健康数据权限
void _showPermissionDeniedDialog() {
  // 这里可以显示一个对话框，引导用户去设置中开启权限
  // 实际实现需要在UI层面完成
  print('提示：请前往 设置 > 隐私与安全性 > 健康 > KcalFit 中开启健康数据访问权限');
}

/// 处理健康数据获取错误
/// 统一处理健康数据相关的错误情况
void _handleHealthDataError(dynamic error) {
  if (error.toString().contains('authorization')) {
    print('权限相关错误，请检查权限配置');
  } else if (error.toString().contains('unavailable')) {
    print('健康数据服务不可用');
  } else {
    print('未知错误: $error');
  }
}

/// KcalFit应用程序主类
/// 配置应用的基本设置、主题、路由和本地化
class KcalFitApp extends StatelessWidget {
  const KcalFitApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeService = Get.find<ThemeService>();

    return GetMaterialApp(
      // 应用基本信息
      title: "KcalFit - 卡路里记录",

      // 本地化配置
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [Locale('zh', 'CN'), Locale('en', 'US')],
      translationsKeys: AppTranslation.translations,
      locale: const Locale('zh', 'CN'),
      fallbackLocale: const Locale('en', 'US'),

      // 路由配置
      initialRoute: AppPages.initial,
      getPages: AppPages.routes,
      routingCallback: _handleRoutingChange,

      // UI配置 - 使用主题服务提供的主题
      theme: themeService.getCurrentTheme(),
      defaultTransition: Transition.cupertino,
      debugShowCheckedModeBanner: false,

      // GetX配置
      enableLog: true,
      popGesture: true,
    );
  }
}

/// 处理路由变化的回调函数
/// 在特定路由变化时执行相应的业务逻辑
void _handleRoutingChange(Routing? routing) {
  if (routing?.current == Routes.HOME) {
    // 返回首页时刷新数据
    _refreshHomeData();
  }
}

/// 刷新首页数据
/// 当用户返回首页时，刷新相关数据以确保数据的实时性
void _refreshHomeData() {
  try {
    // 检查IndexController是否已注册
    if (Get.isRegistered<IndexController>()) {
      Get.find<IndexController>().refreshTodayData();
    }
  } catch (e) {
    // 如果控制器未注册，忽略错误
    // 这种情况通常发生在应用首次启动时
  }
}
