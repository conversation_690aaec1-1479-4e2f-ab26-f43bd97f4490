import 'dart:math';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:speech_to_text/speech_recognition_error.dart' as stt;
import 'package:speech_to_text/speech_recognition_result.dart' as stt;
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:speech_to_text/speech_to_text.dart';

var logger = Logger();

// ignore: slash_for_doc_comments
/**
 * 语音识别工具类
 * - handleVoiceInput 方法用于处理语音输入
 * - handleSpeechStatus 方法用于处理语音识别状态
 * - handleSpeechError 方法用于处理语音识别错误
 * - handleSpeechResult 方法用于处理语音识别结果
 * - startListening 方法用于开始监听语音输入
 * - stopListening 方法用于停止监听语音输入
 * - cancelListening 方法用于取消监听语音输入
 * - cancel 方法用于取消语音识别
 * - isAvailable 方法用于检查语音识别是否可用
 * - locales 方法用于获取支持的语言列表
 * - isListening 方法用于检查是否正在监听语音输入
 * - isNotListening 方法用于检查是否不在监听语音输入
 * - isPaused 方法用于检查是否暂停监听语音输入
 * - isFinalizing 方法用于检查是否正在最终化语音输入
 * - isFinalized 方法用于检查是否已经最终化语音输入
 * - isCanceled 方法用于检查是否已经取消语音识别
 * - isError 方法用于检查是否发生错误
 * - isTimeout 方法用于检查是否超时
 * - isNoMatch 方法用于检查是否没有匹配的语音输入
 * - isPermissionDenied 方法用于检查是否拒绝了权限
 * - isNetworkError 方法用于检查是否发生网络错误
 * - isOtherError 方法用于检查是否发生其他错误

 */

class SpeechToTextUtil {
  SpeechToTextUtil() {
    logger.i("初始化语音识别实例 SpeechToTextUtil");
  }

  final _speech = stt.SpeechToText(); // 语音识别实例
  final _soundLevel = 0.0.obs; // 声音级别 (-80dB静音 ~ 0dB 声音大)
  final _isSoundTooLow = false.obs; // 声音是否太低
  var recognizedWords = ''.obs; // 临时监听到的内容
  var finalRecognizedWords = ''.obs; // 最终监听到的内容

  // handleVoiceInput 方法用于处理语音输入
  // 该方法在用户点击语音输入按钮时调用
  // 该方法会检查设备是否支持中文语音识别
  Future<void> handleVoiceInput() async {
    try {
      logger.i("语音识别是否已成功初始化: ${_speech.isAvailable}");
      // isAvailable 表示语音识别是否已成功初始化
      // 为 true 时表示可以开始语音识别
      //为 false 时表示需要先调用 initialize() 方法初始化
      if (!_speech.isAvailable) {
        //初始化语音识别实例
        await _initSpeechRecognition();
      }

      final locales = await _speech.locales();
      final hasChinese = locales.any((locale) => locale.localeId == 'zh-CN');
      if (!hasChinese) {
        Get.snackbar('提示', '不支持中文语音识别');
        return;
      }

      logger.i("开始语音输入${_speech.isListening}");
      if (_speech.isListening) {
        logger.i("停止监听");
        _speech.stop();
        Get.back();
      } else {
        logger.i("开始监听");
        startListening();
      }
    } catch (e) {
      logger.e('语音识别异常: $e');
      Get.snackbar('错误', '语音识别异常: $e');
    }
  }

  // _initSpeechRecognition 方法用于初始化语音识别
  // 该方法在第一次调用 handleVoiceInput 方法时调用
  Future<void> _initSpeechRecognition() async {
    logger.i("_initSpeechRecognition 初始化语音识别");
    // 初始化语音识别实例
    // onStatus 回调函数用于处理语音识别状态
    // onError 回调函数用于处理语音识别错误
    await _speech.initialize(
      onStatus: handleSpeechStatus, // 注册状态回调
      onError: handleSpeechError,
    );
  }

  // handleSpeechStatus 方法用于处理语音识别状态
  // 该方法在语音识别开始、结束、暂停、继续、取消等状态发生变化时调用
  void handleSpeechStatus(String status) {
    logger.i('语音识别状态: $status');
    switch (status) {
      case 'listening':
        // 开始监听时的处理
        break;
      case 'notListening':
        // 停止监听时的处理
        break;
      case 'done':
        // 识别完成时的处理
        break;
    }
  }

  // 处理语音识别错误
  void handleSpeechError(stt.SpeechRecognitionError error) {
    logger.e('语音识别异常: ${error.errorMsg}, permanent: ${error.permanent}');
    if (error.permanent) {
      // 永久性错误，需要重新初始化
      _speech.cancel();
      if (Get.isDialogOpen ?? false) Get.back();
      Get.snackbar('语音识别失败', '请检查麦克风权限或稍后再试');
    } else {
      // 临时性错误，可以自动重试
      if (Get.isDialogOpen ?? false) {
        Get.back();
        Future.delayed(Duration(seconds: 1), () => startListening()); // 延迟1秒后重试
      }
    }
    _soundLevel.value = 0.0;
    _isSoundTooLow.value = false;
  }

  // 处理语音输入 level (-80dB静音 ~ 0dB 声音大)
  void handleSoundLevelChange(double? level) {
    final rawLevel = level ?? 0.1; // 确保level不为null
    _soundLevel.value = rawLevel; // 计算声音级别
    _isSoundTooLow.value = rawLevel < -30; // 检查声音是否太低
  }

  // 开始监听语音输入
  void startListening() {
    logger.i("开始监听语音输入");
    _soundLevel.value = 0.0;
    _isSoundTooLow.value = false;
    if (!_speech.isAvailable) {
      logger.e('语音识别不可用，请检查初始化状态');
      throw Exception('语音识别服务未初始化或不可用，请确保已调用initialize()方法');
    }
    speechdDialog();
    try {
      /// 启动语音识别监听
      /// 持续接收语音输入直到超时或手动停止
      /// 通过回调函数返回识别结果
      _speech
          .listen(
            localeId: 'zh-CN', // 识别语言设置
            listenFor: Duration(minutes: 5), // 最长监听时间
            pauseFor: Duration(seconds: 10), // 静音自动暂停时间
            // 监听选项
            listenOptions: SpeechListenOptions(
              partialResults: true, // 是否返回临时结果
              onDevice: false, // 是否使用设备本地识别
              listenMode: ListenMode.dictation, // 听写模式
              sampleRate: 44100, // 采样率
              cancelOnError: true, // 出错时自动取消
            ),
            onSoundLevelChange: handleSoundLevelChange, // 音量变化回调
            onResult: handleSpeechRecognitionResult, // 识别结果回调
          )
          .catchError((error) {
            if (Get.isDialogOpen ?? false) Get.back();
            Get.snackbar('错误', '无法开始语音识别: $error');
          });
    } catch (e) {
      if (Get.isDialogOpen ?? false) Get.back();
      logger.e('语音识别初始化失败: $e');
      Get.snackbar('错误', '语音识别初始化失败: $e');
    }
  }

  // 处理语音识别结果
  // 保留输入框已存在的内容，新获取到的御语音内容追加到后面
  void handleSpeechRecognitionResult(stt.SpeechRecognitionResult result) {
    final String text = result.recognizedWords; // 识别出的文本内容
    final bool isFinal = result.finalResult; //是否是最终识别结果
    final double confidence = result.confidence; //识别置信度(0.0-1.0)，部分平台支持
    logger.i("$text 可信度：$confidence isFinal: $isFinal");

    // 使用示例：
    if (isFinal) {
      finalRecognizedWords.value = text;
      // 最终结果处理
      // textEditingController.text = "${description.value} $text";
      // description.value = "${description.value}  $text";
    } else {
      recognizedWords.value = text;
      // 临时结果处理
      // textEditingController.text = "${description.value}  $text";
    }
  }

  Future<dynamic> speechdDialog() {
    logger.i("speechdDialog 显示语音识别对话框");
    return Get.dialog(
      AlertDialog(
        content: Obx(
          () => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 100,
                height: 100,
                child: ClipRect(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Color.alphaBlend(
                            Colors.blue.withAlpha((0.6 * 255).round()),
                            Colors.transparent,
                          ),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(Icons.mic, color: Colors.white, size: 24),
                      ),
                      AnimatedContainer(
                        duration: Duration(milliseconds: 100),
                        constraints: BoxConstraints(
                          minWidth: 60,
                          maxWidth: 240,
                          minHeight: 60,
                          maxHeight: 240,
                        ),
                        width: (80 + _soundLevel.value),
                        height: (80 + _soundLevel.value),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.blue,
                            width: max(1, min(10, 80 + _soundLevel.value)),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              //${(50 + soundLevel.value) * 160}dB
              Text('${_soundLevel.value}dB', style: TextStyle(fontSize: 14)),
              if (_isSoundTooLow.value)
                Text(
                  '声音太小，请靠近麦克风说话',
                  style: TextStyle(color: Colors.red, fontSize: 14),
                )
              else
                Text(
                  "请描述你的饮食内容...",
                  style: TextStyle(color: Colors.green, fontSize: 14),
                ),

              Text('正在聆听...'),

              SizedBox(height: 20),
              ElevatedButton(
                onPressed: () {
                  // ignore: avoid_print
                  print("停止聆听${Get.isDialogOpen}");
                  // ignore: avoid_print
                  print("停止聆听1${Get.isDialogOpen ?? false}");
                  _speech.stop();
                  if (Get.isDialogOpen ?? false) Get.back(); // 关闭对话框
                },
                child: Text('停止聆听'),
              ),
            ],
          ),
        ),
      ),
      barrierDismissible: false, // 点击遮罩层不关闭对话框
    );
  }

  //销毁语音识别实例
  void dispose() {
    _speech.cancel();
  }
}
